import React from 'react';
import Layout from '@/components/Layout';
import DetailedQuoteForm from '@/components/DetailedQuoteForm';

const QuotePage: React.FC = () => {
  return (
    <Layout
      title="Get Your Moving Quote - VayuVector"
      description="Get a detailed quote for your international move. Our comprehensive quote system provides accurate pricing for all your relocation needs."
    >
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container-custom">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Get Your Detailed Moving Quote
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Complete our comprehensive form to receive an accurate quote for your 
              international relocation. Our experts will review your requirements and 
              provide a detailed estimate within 24 hours.
            </p>
            
            {/* Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="flex items-center justify-center space-x-3">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 bg-primary-600 rounded-full"></div>
                </div>
                <span className="text-gray-700">Free & No Obligation</span>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <div className="w-8 h-8 bg-secondary-100 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 bg-secondary-600 rounded-full"></div>
                </div>
                <span className="text-gray-700">Detailed Breakdown</span>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <div className="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 bg-accent-600 rounded-full"></div>
                </div>
                <span className="text-gray-700">Valid for 30 Days</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quote Form Section */}
      <section className="section">
        <div className="container-custom">
          <DetailedQuoteForm />
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose VayuVector?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We're committed to making your international move as smooth and 
              stress-free as possible.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 bg-primary-600 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Transparent Pricing
              </h3>
              <p className="text-gray-600 text-sm">
                No hidden fees or surprise charges. What you see is what you pay.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 bg-secondary-600 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Expert Support
              </h3>
              <p className="text-gray-600 text-sm">
                Dedicated move coordinators guide you through every step.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 bg-accent-600 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Global Network
              </h3>
              <p className="text-gray-600 text-sm">
                Trusted partners in over 120 countries worldwide.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 bg-green-600 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Full Insurance
              </h3>
              <p className="text-gray-600 text-sm">
                Comprehensive coverage for your peace of mind.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-lg text-gray-600">
                Common questions about our quote process
              </p>
            </div>
            
            <div className="space-y-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  How accurate is the online quote?
                </h3>
                <p className="text-gray-600">
                  Our online quote provides a reliable estimate based on the information you provide. 
                  For the most accurate pricing, we recommend scheduling a virtual or in-home survey.
                </p>
              </div>
              
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  How long is the quote valid?
                </h3>
                <p className="text-gray-600">
                  All quotes are valid for 30 days from the date of issue. This gives you time 
                  to make your decision without pressure.
                </p>
              </div>
              
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  What's included in the quote?
                </h3>
                <p className="text-gray-600">
                  Our comprehensive quote includes packing materials, labor, transportation, 
                  insurance, and customs clearance. Additional services are clearly itemized.
                </p>
              </div>
              
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Can I modify my quote later?
                </h3>
                <p className="text-gray-600">
                  Yes, you can update your requirements anytime before booking. We'll adjust 
                  the quote accordingly and send you an updated estimate.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-600 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Need Help with Your Quote?
          </h2>
          <p className="text-lg mb-8 text-primary-100">
            Our relocation experts are here to help you every step of the way.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="tel:******-VAYU-VEC"
              className="btn bg-white text-primary-600 hover:bg-gray-100"
            >
              Call ******-VAYU-VEC
            </a>
            <a
              href="/contact"
              className="btn border-2 border-white text-white hover:bg-white hover:text-primary-600"
            >
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default QuotePage;
