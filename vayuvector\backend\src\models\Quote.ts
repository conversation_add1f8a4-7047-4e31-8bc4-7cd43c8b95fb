import mongoose, { Document, Schema } from 'mongoose';

export interface IQuote extends Document {
  quoteNumber: string;
  customer: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  origin: {
    country: string;
    city: string;
    address?: string;
    zipCode?: string;
  };
  destination: {
    country: string;
    city: string;
    address?: string;
    zipCode?: string;
  };
  moveDetails: {
    homeSize: 'studio' | '1-bedroom' | '2-bedroom' | '3-bedroom' | '4-bedroom' | '5-bedroom' | 'office';
    moveDate: Date;
    flexibility: 'exact' | 'within-week' | 'within-month';
    packingService: boolean;
    storageNeeded: boolean;
    storageDuration?: number; // in months
    specialItems: string[];
    additionalServices: string[];
  };
  vehicles?: {
    type: 'car' | 'motorcycle' | 'boat' | 'rv';
    make: string;
    model: string;
    year: number;
    condition: 'running' | 'non-running';
  }[];
  pricing: {
    basePrice: number;
    packingPrice: number;
    storagePrice: number;
    vehiclePrice: number;
    additionalServicesPrice: number;
    totalPrice: number;
    currency: string;
    validUntil: Date;
  };
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'declined' | 'expired';
  notes?: string;
  assignedAgent?: mongoose.Types.ObjectId;
  followUpDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const QuoteSchema: Schema = new Schema({
  quoteNumber: {
    type: String,
    unique: true,
    required: true,
  },
  customer: {
    firstName: {
      type: String,
      required: [true, 'Customer first name is required'],
      trim: true,
    },
    lastName: {
      type: String,
      required: [true, 'Customer last name is required'],
      trim: true,
    },
    email: {
      type: String,
      required: [true, 'Customer email is required'],
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please add a valid email',
      ],
    },
    phone: {
      type: String,
      required: [true, 'Customer phone is required'],
      match: [/^\+?[1-9]\d{1,14}$/, 'Please add a valid phone number'],
    },
  },
  origin: {
    country: {
      type: String,
      required: [true, 'Origin country is required'],
    },
    city: {
      type: String,
      required: [true, 'Origin city is required'],
    },
    address: String,
    zipCode: String,
  },
  destination: {
    country: {
      type: String,
      required: [true, 'Destination country is required'],
    },
    city: {
      type: String,
      required: [true, 'Destination city is required'],
    },
    address: String,
    zipCode: String,
  },
  moveDetails: {
    homeSize: {
      type: String,
      enum: ['studio', '1-bedroom', '2-bedroom', '3-bedroom', '4-bedroom', '5-bedroom', 'office'],
      required: [true, 'Home size is required'],
    },
    moveDate: {
      type: Date,
      required: [true, 'Move date is required'],
    },
    flexibility: {
      type: String,
      enum: ['exact', 'within-week', 'within-month'],
      default: 'within-week',
    },
    packingService: {
      type: Boolean,
      default: false,
    },
    storageNeeded: {
      type: Boolean,
      default: false,
    },
    storageDuration: {
      type: Number,
      min: 1,
      max: 24,
    },
    specialItems: [String],
    additionalServices: [String],
  },
  vehicles: [{
    type: {
      type: String,
      enum: ['car', 'motorcycle', 'boat', 'rv'],
      required: true,
    },
    make: {
      type: String,
      required: true,
    },
    model: {
      type: String,
      required: true,
    },
    year: {
      type: Number,
      required: true,
      min: 1900,
      max: new Date().getFullYear() + 1,
    },
    condition: {
      type: String,
      enum: ['running', 'non-running'],
      default: 'running',
    },
  }],
  pricing: {
    basePrice: {
      type: Number,
      required: true,
      min: 0,
    },
    packingPrice: {
      type: Number,
      default: 0,
      min: 0,
    },
    storagePrice: {
      type: Number,
      default: 0,
      min: 0,
    },
    vehiclePrice: {
      type: Number,
      default: 0,
      min: 0,
    },
    additionalServicesPrice: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalPrice: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true,
    },
    validUntil: {
      type: Date,
      required: true,
    },
  },
  status: {
    type: String,
    enum: ['draft', 'sent', 'viewed', 'accepted', 'declined', 'expired'],
    default: 'draft',
  },
  notes: String,
  assignedAgent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  followUpDate: Date,
}, {
  timestamps: true,
});

// Generate quote number before saving
QuoteSchema.pre('save', async function (next) {
  if (!this.quoteNumber) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Find the last quote of the day
    const lastQuote = await mongoose.model('Quote').findOne({
      quoteNumber: new RegExp(`^VV${year}${month}${day}`),
    }).sort({ quoteNumber: -1 });
    
    let sequence = 1;
    if (lastQuote) {
      const lastSequence = parseInt(lastQuote.quoteNumber.slice(-3));
      sequence = lastSequence + 1;
    }
    
    this.quoteNumber = `VV${year}${month}${day}${String(sequence).padStart(3, '0')}`;
  }
  next();
});

// Calculate total price before saving
QuoteSchema.pre('save', function (next) {
  this.pricing.totalPrice = 
    this.pricing.basePrice +
    this.pricing.packingPrice +
    this.pricing.storagePrice +
    this.pricing.vehiclePrice +
    this.pricing.additionalServicesPrice;
  next();
});

export default mongoose.model<IQuote>('Quote', QuoteSchema);
