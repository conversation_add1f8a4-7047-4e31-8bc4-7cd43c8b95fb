<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for wind effect on dark background -->
    <linearGradient id="windGradientDark" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0891B2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F97316;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient for text on dark background -->
    <linearGradient id="textGradientDark" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891B2;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow effect for dark background -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle (subtle globe representation) -->
  <circle cx="30" cy="30" r="25" fill="none" stroke="#0891B2" stroke-width="0.8" opacity="0.5"/>
  
  <!-- Wind current lines (flowing curves) with glow -->
  <path d="M5 20 Q15 15 25 20 T45 20" stroke="url(#windGradientDark)" stroke-width="2.5" fill="none" filter="url(#glow)">
    <animate attributeName="d" 
             values="M5 20 Q15 15 25 20 T45 20;M5 20 Q15 25 25 20 T45 20;M5 20 Q15 15 25 20 T45 20" 
             dur="3s" 
             repeatCount="indefinite"/>
  </path>
  
  <path d="M8 30 Q18 25 28 30 T48 30" stroke="url(#windGradientDark)" stroke-width="3" fill="none" filter="url(#glow)">
    <animate attributeName="d" 
             values="M8 30 Q18 25 28 30 T48 30;M8 30 Q18 35 28 30 T48 30;M8 30 Q18 25 28 30 T48 30" 
             dur="2.5s" 
             repeatCount="indefinite"/>
  </path>
  
  <path d="M5 40 Q15 35 25 40 T45 40" stroke="url(#windGradientDark)" stroke-width="2.5" fill="none" filter="url(#glow)">
    <animate attributeName="d" 
             values="M5 40 Q15 35 25 40 T45 40;M5 40 Q15 45 25 40 T45 40;M5 40 Q15 35 25 40 T45 40" 
             dur="3.5s" 
             repeatCount="indefinite"/>
  </path>
  
  <!-- Vector arrow (directional element) with enhanced visibility -->
  <g transform="translate(35, 25)">
    <!-- Arrow shaft -->
    <line x1="0" y1="5" x2="15" y2="5" stroke="#F97316" stroke-width="3.5" stroke-linecap="round" filter="url(#glow)"/>
    <!-- Arrow head -->
    <polygon points="15,0 25,5 15,10" fill="#F97316" filter="url(#glow)"/>
    <!-- Arrow tail -->
    <circle cx="0" cy="5" r="2.5" fill="#F97316" filter="url(#glow)"/>
  </g>
  
  <!-- Company name with enhanced visibility -->
  <text x="70" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#textGradientDark)" filter="url(#glow)">
    Vayu
  </text>
  <text x="70" y="45" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#textGradientDark)" filter="url(#glow)">
    Vector
  </text>
  
  <!-- Tagline -->
  <text x="140" y="35" font-family="Arial, sans-serif" font-size="8" fill="#FFFFFF" opacity="0.9">
    Moving Lives, Not Just Belongings
  </text>
</svg>
