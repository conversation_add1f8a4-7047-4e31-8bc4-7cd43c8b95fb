import express, { Request, Response } from 'express';
import { body } from 'express-validator';
import { validateRequest } from '@/middleware/validation';

const router = express.Router();

// Placeholder auth routes
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }),
  body('firstName').trim().isLength({ min: 1 }),
  body('lastName').trim().isLength({ min: 1 }),
], validateRequest, (_req: Request, res: Response) => {
  res.status(501).json({ success: false, error: 'Not implemented yet' });
});

router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').exists(),
], validateRequest, (_req: Request, res: Response) => {
  res.status(501).json({ success: false, error: 'Not implemented yet' });
});

export default router;
