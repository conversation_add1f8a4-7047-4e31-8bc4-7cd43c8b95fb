import { Request, Response, NextFunction } from 'express';
import Quote from '@/models/Quote';
import { logger } from '@/utils/logger';
import { sendEmail } from '@/utils/email';

// @desc    Calculate quote estimate
// @route   POST /api/quotes/calculate
// @access  Public
export const calculateQuote = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const {
      origin,
      destination,
      homeSize,
      packingService = false,
      storageNeeded = false,
      storageDuration = 0,
      vehicles = [],
      additionalServices = [],
    } = req.body;

    // Base pricing logic (simplified for demo)
    const basePrices = {
      'studio': 2000,
      '1-bedroom': 3500,
      '2-bedroom': 5000,
      '3-bedroom': 7500,
      '4-bedroom': 10000,
      '5-bedroom': 12500,
      'office': 8000,
    };

    let basePrice = basePrices[homeSize as keyof typeof basePrices] || 5000;

    // Distance multiplier (simplified)
    const isInternational = origin.country !== destination.country;
    if (isInternational) {
      basePrice *= 1.5;
    }

    // Calculate additional costs
    let packingPrice = 0;
    if (packingService) {
      packingPrice = basePrice * 0.3; // 30% of base price
    }

    let storagePrice = 0;
    if (storageNeeded && storageDuration > 0) {
      storagePrice = 200 * storageDuration; // $200 per month
    }

    let vehiclePrice = 0;
    if (vehicles.length > 0) {
      vehiclePrice = vehicles.length * (isInternational ? 2500 : 1500);
    }

    let additionalServicesPrice = 0;
    if (additionalServices.length > 0) {
      additionalServicesPrice = additionalServices.length * 500;
    }

    const totalPrice = basePrice + packingPrice + storagePrice + vehiclePrice + additionalServicesPrice;

    const estimate = {
      basePrice,
      packingPrice,
      storagePrice,
      vehiclePrice,
      additionalServicesPrice,
      totalPrice,
      currency: 'USD',
      breakdown: {
        homeSize,
        isInternational,
        packingService,
        storageNeeded,
        storageDuration,
        vehicleCount: vehicles.length,
        additionalServiceCount: additionalServices.length,
      },
    };

    res.status(200).json({
      success: true,
      data: estimate,
    });
  } catch (error) {
    logger.error('Error calculating quote:', error);
    next(error);
  }
};

// @desc    Create new quote
// @route   POST /api/quotes
// @access  Private
export const createQuote = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Calculate pricing
    const calculateResult = await calculateQuoteInternal(req.body);
    
    // Set valid until date (30 days from now)
    const validUntil = new Date();
    validUntil.setDate(validUntil.getDate() + 30);

    const quoteData = {
      ...req.body,
      pricing: {
        ...calculateResult,
        validUntil,
      },
      status: 'draft',
    };

    const quote = await Quote.create(quoteData);

    // Send email notification to customer
    try {
      await sendEmail({
        to: quote.customer.email,
        subject: `Your VayuVector Quote - ${quote.quoteNumber}`,
        template: 'quote-created',
        data: {
          customerName: `${quote.customer.firstName} ${quote.customer.lastName}`,
          quoteNumber: quote.quoteNumber,
          totalPrice: quote.pricing.totalPrice,
          currency: quote.pricing.currency,
          validUntil: quote.pricing.validUntil,
          origin: `${quote.origin.city}, ${quote.origin.country}`,
          destination: `${quote.destination.city}, ${quote.destination.country}`,
        },
      });
    } catch (emailError) {
      logger.error('Failed to send quote email:', emailError);
      // Don't fail the request if email fails
    }

    res.status(201).json({
      success: true,
      data: quote,
    });
  } catch (error) {
    logger.error('Error creating quote:', error);
    next(error);
  }
};

// @desc    Get all quotes
// @route   GET /api/quotes
// @access  Private
export const getQuotes = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search as string, 'i');
      filter.$or = [
        { quoteNumber: searchRegex },
        { 'customer.firstName': searchRegex },
        { 'customer.lastName': searchRegex },
        { 'customer.email': searchRegex },
      ];
    }

    const quotes = await Quote.find(filter)
      .populate('assignedAgent', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Quote.countDocuments(filter);

    res.status(200).json({
      success: true,
      count: quotes.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      data: quotes,
    });
  } catch (error) {
    logger.error('Error fetching quotes:', error);
    next(error);
  }
};

// @desc    Get single quote
// @route   GET /api/quotes/:id
// @access  Private
export const getQuote = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const quote = await Quote.findById(req.params.id)
      .populate('assignedAgent', 'firstName lastName email phone');

    if (!quote) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found',
      });
    }

    res.status(200).json({
      success: true,
      data: quote,
    });
  } catch (error) {
    logger.error('Error fetching quote:', error);
    next(error);
  }
};

// @desc    Update quote
// @route   PUT /api/quotes/:id
// @access  Private (Admin/Agent)
export const updateQuote = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let quote = await Quote.findById(req.params.id);

    if (!quote) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found',
      });
    }

    // Recalculate pricing if move details changed
    if (req.body.moveDetails || req.body.vehicles) {
      const calculateResult = await calculateQuoteInternal(req.body);
      req.body.pricing = {
        ...quote.pricing,
        ...calculateResult,
      };
    }

    quote = await Quote.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      data: quote,
    });
  } catch (error) {
    logger.error('Error updating quote:', error);
    next(error);
  }
};

// @desc    Delete quote
// @route   DELETE /api/quotes/:id
// @access  Private (Admin)
export const deleteQuote = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const quote = await Quote.findById(req.params.id);

    if (!quote) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found',
      });
    }

    await quote.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    logger.error('Error deleting quote:', error);
    next(error);
  }
};

// Helper function to calculate quote pricing
const calculateQuoteInternal = async (data: any) => {
  const {
    moveDetails,
    vehicles = [],
    origin,
    destination,
  } = data;

  const basePrices = {
    'studio': 2000,
    '1-bedroom': 3500,
    '2-bedroom': 5000,
    '3-bedroom': 7500,
    '4-bedroom': 10000,
    '5-bedroom': 12500,
    'office': 8000,
  };

  let basePrice = basePrices[moveDetails.homeSize as keyof typeof basePrices] || 5000;

  const isInternational = origin.country !== destination.country;
  if (isInternational) {
    basePrice *= 1.5;
  }

  let packingPrice = 0;
  if (moveDetails.packingService) {
    packingPrice = basePrice * 0.3;
  }

  let storagePrice = 0;
  if (moveDetails.storageNeeded && moveDetails.storageDuration > 0) {
    storagePrice = 200 * moveDetails.storageDuration;
  }

  let vehiclePrice = 0;
  if (vehicles.length > 0) {
    vehiclePrice = vehicles.length * (isInternational ? 2500 : 1500);
  }

  let additionalServicesPrice = 0;
  if (moveDetails.additionalServices && moveDetails.additionalServices.length > 0) {
    additionalServicesPrice = moveDetails.additionalServices.length * 500;
  }

  return {
    basePrice,
    packingPrice,
    storagePrice,
    vehiclePrice,
    additionalServicesPrice,
    currency: 'USD',
  };
};

// @desc    Save quote as draft
// @route   POST /api/quotes/save-draft
// @access  Public
export const saveDraft = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Calculate pricing if move details are provided
    let pricing = {};
    if (req.body.moveDetails && req.body.origin && req.body.destination) {
      const calculateResult = await calculateQuoteInternal(req.body);

      // Set valid until date (30 days from now)
      const validUntil = new Date();
      validUntil.setDate(validUntil.getDate() + 30);

      pricing = {
        ...calculateResult,
        validUntil,
      };
    }

    const quoteData = {
      ...req.body,
      pricing,
      status: 'draft',
    };

    const quote = await Quote.create(quoteData);

    res.status(201).json({
      success: true,
      data: quote,
      message: 'Quote saved as draft',
    });
  } catch (error) {
    logger.error('Error saving quote draft:', error);
    next(error);
  }
};

// @desc    Email quote to customer
// @route   POST /api/quotes/:id/email
// @access  Public
export const emailQuote = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const quote = await Quote.findById(req.params.id);

    if (!quote) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found',
      });
    }

    await sendEmail({
      to: quote.customer.email,
      subject: `Your VayuVector Quote - ${quote.quoteNumber}`,
      template: 'quote-details',
      data: {
        customerName: `${quote.customer.firstName} ${quote.customer.lastName}`,
        quoteNumber: quote.quoteNumber,
        quote: quote,
        totalPrice: quote.pricing.totalPrice,
        currency: quote.pricing.currency,
        validUntil: quote.pricing.validUntil,
        origin: `${quote.origin.city}, ${quote.origin.country}`,
        destination: `${quote.destination.city}, ${quote.destination.country}`,
        moveDate: quote.moveDetails.moveDate,
        homeSize: quote.moveDetails.homeSize,
      },
    });

    // Update quote status to 'sent' if it was draft
    if (quote.status === 'draft') {
      quote.status = 'sent';
      await quote.save();
    }

    res.status(200).json({
      success: true,
      message: 'Quote emailed successfully',
    });
  } catch (error) {
    logger.error('Error emailing quote:', error);
    next(error);
  }
};

// @desc    Get quote by quote number (public access)
// @route   GET /api/quotes/public/:quoteNumber
// @access  Public
export const getQuoteByNumber = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const quote = await Quote.findOne({ quoteNumber: req.params.quoteNumber });

    if (!quote) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found',
      });
    }

    // Mark as viewed if not already
    if (quote.status === 'sent') {
      quote.status = 'viewed';
      await quote.save();
    }

    res.status(200).json({
      success: true,
      data: quote,
    });
  } catch (error) {
    logger.error('Error fetching quote by number:', error);
    next(error);
  }
};
