# VayuVector.com - Global Relocation Services

VayuVector is a comprehensive logistics website targeting international professionals who frequently relocate due to job requirements. We offer door-to-door relocation services including packaging, transportation, and destination setup.

## Project Overview

**Tagline**: "Your Global Relocation Partner - Moving Lives, Not Just Belongings"

### Services Offered
- **Residential Relocation**: Full household packing, furniture handling, storage solutions
- **Vehicle Transportation**: Door-to-door car shipping, motorcycle transport, luxury vehicle handling
- **Documentation & Administrative Support**: Visa guidance, school enrollment, utility setup

## Tech Stack

- **Frontend**: Next.js with Tailwind CSS
- **Backend**: Node.js with Express
- **Database**: MongoDB
- **Containerization**: Docker with docker-compose
- **Deployment**: Docker-ready with Nginx reverse proxy

## Project Structure

```
vayuvector/
├── frontend/                 # Next.js frontend application
│   ├── components/          # Reusable React components
│   ├── pages/              # Next.js pages
│   ├── styles/             # Tailwind CSS and custom styles
│   ├── public/             # Static assets (images, icons, logo)
│   ├── utils/              # Utility functions
│   └── hooks/              # Custom React hooks
├── backend/                 # Node.js/Express backend
│   ├── controllers/        # Route controllers
│   ├── models/             # Database models
│   ├── routes/             # API routes
│   ├── middleware/         # Custom middleware
│   ├── utils/              # Backend utilities
│   └── config/             # Configuration files
├── docker-compose.yml       # Docker compose configuration
├── Dockerfile              # Docker configuration
├── nginx.conf              # Nginx configuration
└── README.md               # This file
```

## Quick Start

### Prerequisites
- Node.js 18+ 
- Docker and Docker Compose
- MongoDB (or use Docker)

### Development Setup

1. **Clone and setup**:
```bash
cd vayuvector
npm run setup  # Installs dependencies for both frontend and backend
```

2. **Environment Configuration**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start development servers**:
```bash
npm run dev  # Starts both frontend and backend in development mode
```

4. **Using Docker**:
```bash
docker-compose up --build
```

### Production Deployment

```bash
docker-compose -f docker-compose.prod.yml up --build -d
```

## Features

### Core Functionality
- ✅ Interactive quote calculator
- ✅ Multi-step quote form with progress tracking
- ✅ Customer portal with shipment tracking
- ✅ Document management system
- ✅ Real-time chat integration
- ✅ Payment gateway integration
- ✅ Multi-language support framework

### Pages
- **Homepage**: Hero section, world map, quick quote, testimonials
- **Services**: Comprehensive service categories with detailed descriptions
- **How It Works**: Visual timeline of the relocation process
- **Pricing**: Transparent pricing with online calculator
- **About Us**: Company story, team profiles, certifications
- **Contact**: Multi-region contacts, live chat, FAQ

### Interactive Tools
- Moving cost calculator
- Customs duty estimator
- Packing space calculator
- Country-specific relocation guides

## Brand Guidelines

### Logo
- **Concept**: Combines "Vayu" (wind) with "Vector" (direction)
- **Elements**: Wind currents, directional arrows, subtle globe
- **Colors**: 
  - Primary: Deep Blue (#1E3A8A)
  - Secondary: Teal (#0891B2) 
  - Accent: Orange (#F97316)

### Design Principles
- Clean, modern, professional
- Mobile-first responsive design
- High-quality imagery focusing on families and professional service
- Smooth animations and micro-interactions

## API Documentation

### Endpoints
- `POST /api/quotes` - Generate moving quote
- `GET /api/quotes/:id` - Retrieve quote details
- `POST /api/contact` - Submit contact form
- `GET /api/services` - List all services
- `POST /api/customers` - Customer registration
- `GET /api/tracking/:id` - Shipment tracking

## Security & Compliance

- SSL/TLS encryption
- GDPR compliant data handling
- Secure form submissions
- Data encryption for sensitive information
- Regular security headers implementation

## Performance

- Core Web Vitals optimized
- Image lazy loading
- Code splitting and optimization
- CDN integration ready
- Caching strategies implemented

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

Proprietary - VayuVector.com

## Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [Link to detailed docs]
- Issue Tracker: [Link to issues]

---

**VayuVector** - Moving Lives, Not Just Belongings
