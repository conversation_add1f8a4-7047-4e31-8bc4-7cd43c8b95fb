/*
 * Copyright © 2009-10 <PERSON>
 *
 * SPDX-License-Identifier: LGPL-2.1-or-later
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 *
 * Author: <PERSON> <<EMAIL>>
 */

#ifndef __G_REGISTRY_SETTINGS_BACKEND_H__
#define __G_REGISTRY_SETTINGS_BACKEND_H__

#include <glib-object.h>

#include <gio/gsettingsbackend.h>

GIO_AVAILABLE_IN_2_78
GSettingsBackend *      g_registry_settings_backend_new     (const gchar     *registry_key);

#endif /* __G_REGISTRY_SETTINGS_BACKEND_H__ */
