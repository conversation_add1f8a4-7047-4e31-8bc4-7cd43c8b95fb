{"name": "vay<PERSON>ctor-backend", "version": "1.0.0", "description": "VayuVector Backend API - Global Relocation Services", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "seed": "ts-node src/scripts/seed.ts", "migrate": "ts-node src/scripts/migrate.ts", "backup": "ts-node src/scripts/backup.ts"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.6.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "stripe": "^14.5.0", "axios": "^1.6.0", "compression": "^1.7.4", "express-mongo-sanitize": "^2.2.0", "hpp": "^0.2.3", "xss": "^1.0.14", "redis": "^4.6.10", "winston": "^3.11.0", "joi": "^17.11.0", "moment": "^2.29.4", "uuid": "^9.0.1", "sharp": "^0.32.6", "pdf-lib": "^1.17.1", "csv-parser": "^3.0.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/node": "^20.8.7", "@types/express": "^4.17.20", "@types/cors": "^2.8.15", "@types/morgan": "^1.9.7", "@types/bcryptjs": "^2.4.5", "@types/jsonwebtoken": "^9.0.4", "@types/multer": "^1.4.9", "@types/nodemailer": "^6.4.13", "@types/compression": "^1.7.4", "@types/hpp": "^0.2.4", "@types/uuid": "^9.0.6", "@types/node-cron": "^3.0.10", "@types/jest": "^29.5.6", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.15", "eslint": "^8.52.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "tsc-alias": "^1.8.8", "prettier": "^3.0.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["logistics", "relocation", "api", "express", "mongodb", "typescript"], "author": "VayuVector Team", "license": "PROPRIETARY"}