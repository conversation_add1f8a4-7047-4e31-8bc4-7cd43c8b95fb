import nodemailer from 'nodemailer';
import { logger } from './logger';

interface EmailOptions {
  to: string;
  subject: string;
  template: string;
  data: any;
}

// Create transporter
const createTransporter = () => {
  if (process.env.NODE_ENV === 'production') {
    // Production email configuration
    return nodemailer.createTransporter({
      service: process.env.EMAIL_SERVICE || 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
  } else {
    // Development configuration (using Ethereal for testing)
    return nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      auth: {
        user: '<EMAIL>',
        pass: 'ethereal.pass',
      },
    });
  }
};

// Email templates
const getEmailTemplate = (template: string, data: any): { html: string; text: string } => {
  switch (template) {
    case 'quote-created':
      return {
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #1E3A8A 0%, #0891B2 100%); padding: 40px 20px; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 28px;">VayuVector</h1>
              <p style="color: #E0E7FF; margin: 10px 0 0 0;">Your Global Relocation Partner</p>
            </div>
            
            <div style="padding: 40px 20px;">
              <h2 style="color: #1E3A8A; margin-bottom: 20px;">Your Quote is Ready!</h2>
              
              <p>Dear ${data.customerName},</p>
              
              <p>Thank you for choosing VayuVector for your international relocation needs. We've prepared your personalized quote:</p>
              
              <div style="background: #F8FAFC; border-left: 4px solid #0891B2; padding: 20px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #1E3A8A;">Quote #${data.quoteNumber}</h3>
                <p style="margin: 5px 0;"><strong>Route:</strong> ${data.origin} → ${data.destination}</p>
                <p style="margin: 5px 0;"><strong>Total Estimate:</strong> ${data.currency} ${data.totalPrice.toLocaleString()}</p>
                <p style="margin: 5px 0;"><strong>Valid Until:</strong> ${new Date(data.validUntil).toLocaleDateString()}</p>
              </div>
              
              <p>This quote includes:</p>
              <ul>
                <li>Professional packing and unpacking</li>
                <li>Door-to-door transportation</li>
                <li>Customs clearance assistance</li>
                <li>Full insurance coverage</li>
                <li>Dedicated move coordinator</li>
              </ul>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="https://vayuvector.com/quotes/public/${data.quoteNumber}" 
                   style="background: #F97316; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  View Full Quote
                </a>
              </div>
              
              <p>Questions? Our team is here to help:</p>
              <ul>
                <li>Phone: ******-VAYU-VEC</li>
                <li>Email: <EMAIL></li>
                <li>Live Chat: Available 24/7 on our website</li>
              </ul>
              
              <p>Best regards,<br>The VayuVector Team</p>
            </div>
            
            <div style="background: #F1F5F9; padding: 20px; text-align: center; font-size: 12px; color: #64748B;">
              <p>VayuVector - Moving Lives, Not Just Belongings</p>
              <p>123 Logistics Ave, Global City, GC 12345</p>
            </div>
          </div>
        `,
        text: `
VayuVector - Your Quote is Ready!

Dear ${data.customerName},

Thank you for choosing VayuVector for your international relocation needs.

Quote #${data.quoteNumber}
Route: ${data.origin} → ${data.destination}
Total Estimate: ${data.currency} ${data.totalPrice.toLocaleString()}
Valid Until: ${new Date(data.validUntil).toLocaleDateString()}

This quote includes professional packing, door-to-door transportation, customs clearance, full insurance, and a dedicated move coordinator.

View your full quote: https://vayuvector.com/quotes/public/${data.quoteNumber}

Questions? Contact us:
Phone: ******-VAYU-VEC
Email: <EMAIL>

Best regards,
The VayuVector Team
        `,
      };

    case 'quote-details':
      return {
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #1E3A8A 0%, #0891B2 100%); padding: 40px 20px; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 28px;">VayuVector</h1>
              <p style="color: #E0E7FF; margin: 10px 0 0 0;">Detailed Quote Information</p>
            </div>
            
            <div style="padding: 40px 20px;">
              <h2 style="color: #1E3A8A; margin-bottom: 20px;">Quote Details</h2>
              
              <p>Dear ${data.customerName},</p>
              
              <p>Here are the complete details of your moving quote:</p>
              
              <div style="background: #F8FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="margin: 0 0 15px 0; color: #1E3A8A;">Quote #${data.quoteNumber}</h3>
                
                <div style="margin-bottom: 15px;">
                  <strong>Move Route:</strong><br>
                  From: ${data.origin}<br>
                  To: ${data.destination}
                </div>
                
                <div style="margin-bottom: 15px;">
                  <strong>Move Details:</strong><br>
                  Home Size: ${data.homeSize}<br>
                  Move Date: ${new Date(data.moveDate).toLocaleDateString()}
                </div>
                
                <div style="background: white; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <h4 style="margin: 0 0 10px 0; color: #1E3A8A;">Pricing Breakdown</h4>
                  <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Base Moving Cost:</span>
                    <span>${data.currency} ${data.quote.pricing.basePrice.toLocaleString()}</span>
                  </div>
                  ${data.quote.pricing.packingPrice > 0 ? `
                  <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Packing Service:</span>
                    <span>${data.currency} ${data.quote.pricing.packingPrice.toLocaleString()}</span>
                  </div>
                  ` : ''}
                  ${data.quote.pricing.storagePrice > 0 ? `
                  <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Storage Service:</span>
                    <span>${data.currency} ${data.quote.pricing.storagePrice.toLocaleString()}</span>
                  </div>
                  ` : ''}
                  ${data.quote.pricing.vehiclePrice > 0 ? `
                  <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Vehicle Transportation:</span>
                    <span>${data.currency} ${data.quote.pricing.vehiclePrice.toLocaleString()}</span>
                  </div>
                  ` : ''}
                  <hr style="margin: 10px 0; border: none; border-top: 1px solid #E2E8F0;">
                  <div style="display: flex; justify-content: space-between; margin: 5px 0; font-weight: bold; font-size: 18px; color: #1E3A8A;">
                    <span>Total:</span>
                    <span>${data.currency} ${data.totalPrice.toLocaleString()}</span>
                  </div>
                </div>
                
                <p style="margin: 15px 0 0 0; font-size: 14px; color: #64748B;">
                  <strong>Valid Until:</strong> ${new Date(data.validUntil).toLocaleDateString()}
                </p>
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="https://vayuvector.com/quotes/public/${data.quoteNumber}" 
                   style="background: #F97316; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-right: 10px;">
                  View Online
                </a>
                <a href="https://vayuvector.com/book?quote=${data.quoteNumber}" 
                   style="background: #1E3A8A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Book This Move
                </a>
              </div>
              
              <p>Ready to proceed? Contact us to book your move or if you have any questions:</p>
              <ul>
                <li>Phone: ******-VAYU-VEC</li>
                <li>Email: <EMAIL></li>
                <li>Live Chat: Available 24/7</li>
              </ul>
              
              <p>Best regards,<br>The VayuVector Team</p>
            </div>
          </div>
        `,
        text: `
VayuVector - Quote Details

Dear ${data.customerName},

Quote #${data.quoteNumber}
Move Route: ${data.origin} → ${data.destination}
Home Size: ${data.homeSize}
Move Date: ${new Date(data.moveDate).toLocaleDateString()}

Pricing Breakdown:
Base Moving Cost: ${data.currency} ${data.quote.pricing.basePrice.toLocaleString()}
${data.quote.pricing.packingPrice > 0 ? `Packing Service: ${data.currency} ${data.quote.pricing.packingPrice.toLocaleString()}\n` : ''}
${data.quote.pricing.storagePrice > 0 ? `Storage Service: ${data.currency} ${data.quote.pricing.storagePrice.toLocaleString()}\n` : ''}
${data.quote.pricing.vehiclePrice > 0 ? `Vehicle Transportation: ${data.currency} ${data.quote.pricing.vehiclePrice.toLocaleString()}\n` : ''}
Total: ${data.currency} ${data.totalPrice.toLocaleString()}

Valid Until: ${new Date(data.validUntil).toLocaleDateString()}

View online: https://vayuvector.com/quotes/public/${data.quoteNumber}
Book this move: https://vayuvector.com/book?quote=${data.quoteNumber}

Contact us:
Phone: ******-VAYU-VEC
Email: <EMAIL>

Best regards,
The VayuVector Team
        `,
      };

    default:
      return {
        html: '<p>Email template not found</p>',
        text: 'Email template not found',
      };
  }
};

export const sendEmail = async (options: EmailOptions): Promise<void> => {
  try {
    const transporter = createTransporter();
    const { html, text } = getEmailTemplate(options.template, options.data);

    const mailOptions = {
      from: `"VayuVector" <${process.env.EMAIL_USER || '<EMAIL>'}>`,
      to: options.to,
      subject: options.subject,
      html,
      text,
    };

    const info = await transporter.sendMail(mailOptions);
    
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Email sent: ${info.messageId}`);
      logger.info(`Preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    } else {
      logger.info(`Email sent to ${options.to}: ${info.messageId}`);
    }
  } catch (error) {
    logger.error('Error sending email:', error);
    throw error;
  }
};
