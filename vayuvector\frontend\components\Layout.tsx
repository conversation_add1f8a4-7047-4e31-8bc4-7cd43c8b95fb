import React from 'react';
import Head from 'next/head';
import Header from './Header';
import Footer from './Footer';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  noIndex?: boolean;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  title = 'VayuVector - Your Global Relocation Partner',
  description = 'Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support.',
  keywords = 'international moving, relocation services, global shipping, door-to-door moving, expat services, international logistics',
  ogImage = '/images/og-image.jpg',
  noIndex = false,
}) => {
  const fullTitle = title.includes('VayuVector') ? title : `${title} | VayuVector`;

  return (
    <>
      <Head>
        <title>{fullTitle}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta charSet="utf-8" />
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content={fullTitle} />
        <meta property="og:description" content={description} />
        <meta property="og:image" content={ogImage} />
        <meta property="og:site_name" content="VayuVector" />
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={fullTitle} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={ogImage} />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        
        {/* Canonical URL */}
        <link rel="canonical" href={`https://vayuvector.com${typeof window !== 'undefined' ? window.location.pathname : ''}`} />
        
        {/* No index for development or specific pages */}
        {noIndex && <meta name="robots" content="noindex, nofollow" />}
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Schema.org markup */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "VayuVector",
              "description": description,
              "url": "https://vayuvector.com",
              "logo": "https://vayuvector.com/logo.svg",
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "******-VAYU-VEC",
                "contactType": "customer service",
                "availableLanguage": ["English"]
              },
              "sameAs": [
                "https://facebook.com/vayuvector",
                "https://twitter.com/vayuvector",
                "https://linkedin.com/company/vayuvector"
              ],
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "123 Logistics Ave",
                "addressLocality": "Global City",
                "addressRegion": "GC",
                "postalCode": "12345",
                "addressCountry": "US"
              }
            })
          }}
        />
      </Head>
      
      <div className="min-h-screen flex flex-col bg-white">
        <Header />
        
        <main className="flex-grow">
          {children}
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default Layout;
