<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for wind effect -->
    <linearGradient id="windGradientIcon" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0891B2;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F97316;stop-opacity:0.9" />
    </linearGradient>
    
    <!-- Drop shadow filter -->
    <filter id="dropShadowIcon" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="30" cy="30" r="28" fill="#FFFFFF" stroke="#1E3A8A" stroke-width="2" filter="url(#dropShadowIcon)"/>
  
  <!-- Wind current lines -->
  <path d="M10 20 Q20 15 30 20 T50 20" stroke="url(#windGradientIcon)" stroke-width="3" fill="none">
    <animate attributeName="d" 
             values="M10 20 Q20 15 30 20 T50 20;M10 20 Q20 25 30 20 T50 20;M10 20 Q20 15 30 20 T50 20" 
             dur="3s" 
             repeatCount="indefinite"/>
  </path>
  
  <path d="M12 30 Q22 25 32 30 T52 30" stroke="url(#windGradientIcon)" stroke-width="4" fill="none">
    <animate attributeName="d" 
             values="M12 30 Q22 25 32 30 T52 30;M12 30 Q22 35 32 30 T52 30;M12 30 Q22 25 32 30 T52 30" 
             dur="2.5s" 
             repeatCount="indefinite"/>
  </path>
  
  <path d="M10 40 Q20 35 30 40 T50 40" stroke="url(#windGradientIcon)" stroke-width="3" fill="none">
    <animate attributeName="d" 
             values="M10 40 Q20 35 30 40 T50 40;M10 40 Q20 45 30 40 T50 40;M10 40 Q20 35 30 40 T50 40" 
             dur="3.5s" 
             repeatCount="indefinite"/>
  </path>
  
  <!-- Vector arrow -->
  <g transform="translate(25, 25)">
    <!-- Arrow shaft -->
    <line x1="0" y1="5" x2="12" y2="5" stroke="#F97316" stroke-width="4" stroke-linecap="round"/>
    <!-- Arrow head -->
    <polygon points="12,0 20,5 12,10" fill="#F97316"/>
    <!-- Arrow tail -->
    <circle cx="0" cy="5" r="3" fill="#F97316"/>
  </g>
  
  <!-- Small "V" initial -->
  <text x="30" y="52" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1E3A8A" text-anchor="middle">
    V
  </text>
</svg>
