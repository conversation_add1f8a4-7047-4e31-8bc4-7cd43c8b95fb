{"name": "v<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "VayuVector - Global Relocation Services Platform", "main": "index.js", "scripts": {"setup": "npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "docker:dev": "docker-compose up --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up --build -d", "docker:down": "docker-compose down", "docker:clean": "docker-compose down -v --rmi all", "logs": "docker-compose logs -f", "db:seed": "cd backend && npm run seed", "db:migrate": "cd backend && npm run migrate", "backup": "cd backend && npm run backup", "deploy": "npm run build && npm run docker:prod"}, "keywords": ["logistics", "relocation", "moving", "international", "shipping", "nextjs", "nodejs", "mongodb"], "author": "VayuVector Team", "license": "PROPRIETARY", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/vayuvector/vayuvector-website.git"}, "bugs": {"url": "https://github.com/vayuvector/vayuvector-website/issues"}, "homepage": "https://vayuvector.com"}