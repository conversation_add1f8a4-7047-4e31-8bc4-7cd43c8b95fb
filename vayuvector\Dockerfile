# Multi-stage Dockerfile for VayuVector
# This Dockerfile builds both frontend and backend in a single container for development

FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies for both frontend and backend
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build frontend
WORKDIR /app/frontend
RUN npm run build

# Build backend
WORKDIR /app/backend
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built applications
COPY --from=builder /app/frontend/public ./frontend/public
COPY --from=builder /app/frontend/.next/standalone ./frontend/
COPY --from=builder /app/frontend/.next/static ./frontend/.next/static
COPY --from=builder /app/backend/dist ./backend/
COPY --from=builder /app/backend/node_modules ./backend/node_modules

USER nextjs

EXPOSE 3000
EXPOSE 5000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Start both services
CMD ["sh", "-c", "cd /app/backend && node server.js & cd /app/frontend && node server.js"]
