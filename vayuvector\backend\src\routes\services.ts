import express from 'express';

const router = express.Router();

// Get all services
router.get('/', (_req, res) => {
  const services = [
    {
      id: 'residential',
      name: 'Residential Relocation',
      description: 'Complete household moving services',
      features: ['Packing & Unpacking', 'Furniture Assembly', 'Storage Solutions'],
    },
    {
      id: 'vehicle',
      name: 'Vehicle Transportation',
      description: 'Safe vehicle shipping worldwide',
      features: ['Door-to-door Service', 'Insurance Coverage', 'Real-time Tracking'],
    },
    {
      id: 'documentation',
      name: 'Documentation Support',
      description: 'Complete paperwork assistance',
      features: ['Visa Support', 'Customs Clearance', 'Legal Documentation'],
    },
  ];

  res.status(200).json({
    success: true,
    data: services,
  });
});

// Get single service
router.get('/:id', (_req, res) => {
  res.status(501).json({ success: false, error: 'Not implemented yet' });
});

export default router;
