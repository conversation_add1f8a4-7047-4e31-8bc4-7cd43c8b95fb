# Environment Configuration for Vayu<PERSON>ector
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
BACKEND_PORT=5000

# Database Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password123
MONGO_DB_NAME=vayuvector

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# WhatsApp Business API
WHATSAPP_API_KEY=your-whatsapp-api-key
NEXT_PUBLIC_WHATSAPP_NUMBER=+1234567890

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Payment Gateway (Stripe)
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000/api

# Production URLs (for production deployment)
# NEXT_PUBLIC_API_URL=https://api.vayuvector.com/api

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# Rate Limiting
API_RATE_LIMIT=100  # requests per 15 minutes
CONTACT_RATE_LIMIT=5  # contact form submissions per hour

# Analytics
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Social Media
FACEBOOK_PAGE_URL=https://facebook.com/vayuvector
TWITTER_HANDLE=@vayuvector
LINKEDIN_PAGE_URL=https://linkedin.com/company/vayuvector
INSTAGRAM_HANDLE=@vayuvector

# Company Information
COMPANY_NAME=VayuVector
COMPANY_EMAIL=<EMAIL>
COMPANY_PHONE=******-VAYU-VEC
COMPANY_ADDRESS=123 Logistics Ave, Global City, GC 12345

# Support Configuration
SUPPORT_EMAIL=<EMAIL>
EMERGENCY_CONTACT=******-EMERGENCY

# Feature Flags
ENABLE_LIVE_CHAT=true
ENABLE_QUOTE_CALCULATOR=true
ENABLE_CUSTOMER_PORTAL=true
ENABLE_BLOG=true
ENABLE_MULTILINGUAL=false

# Third-party Integrations
SHIPMENT_TRACKING_API_KEY=your-tracking-api-key
CUSTOMS_API_KEY=your-customs-api-key

# SSL Configuration (for production)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info
