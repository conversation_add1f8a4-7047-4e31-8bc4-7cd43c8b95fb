import React from 'react';
import Layout from '@/components/Layout';

const HomePage: React.FC = () => {
  return (
    <Layout
      title="VayuVector - Your Global Relocation Partner"
      description="Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support for expats and professionals worldwide."
    >
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-700 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-hero-pattern"></div>
        </div>
        
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-accent-500/20 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="relative z-10 container-custom text-center text-white">
          <div className="max-w-4xl mx-auto">
            {/* Main Headline */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
              Your Global
              <span className="block text-gradient-accent">
                Relocation Partner
              </span>
            </h1>
            
            {/* Tagline */}
            <p className="text-xl md:text-2xl lg:text-3xl mb-8 text-gray-200 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              Moving Lives, Not Just Belongings
            </p>
            
            {/* Description */}
            <p className="text-lg md:text-xl mb-12 text-gray-300 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              Professional door-to-door relocation services for international professionals. 
              From packing to destination setup, we handle every detail of your global move.
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              <button className="btn-accent text-lg px-8 py-4 shadow-glow-accent hover:shadow-glow-accent">
                Get Free Quote
              </button>
              <button className="btn-outline text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-primary-900">
                How It Works
              </button>
            </div>
            
            {/* Trust Indicators */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-accent-400 mb-2">15+</div>
                <div className="text-gray-300">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-accent-400 mb-2">50K+</div>
                <div className="text-gray-300">Successful Moves</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-accent-400 mb-2">120+</div>
                <div className="text-gray-300">Countries Served</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Quick Quote Section */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Get Your Moving Quote in Minutes
              </h2>
              <p className="text-lg text-gray-600">
                Tell us about your move and get an instant estimate
              </p>
            </div>
            
            {/* Quick Quote Form */}
            <div className="card p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Moving From
                  </label>
                  <input
                    type="text"
                    placeholder="Enter city or country"
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Moving To
                  </label>
                  <input
                    type="text"
                    placeholder="Enter city or country"
                    className="input"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Home Size
                  </label>
                  <select className="input">
                    <option>Select home size</option>
                    <option>Studio/1 Bedroom</option>
                    <option>2 Bedroom</option>
                    <option>3 Bedroom</option>
                    <option>4+ Bedroom</option>
                    <option>Office</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Moving Date
                  </label>
                  <input
                    type="date"
                    className="input"
                  />
                </div>
              </div>
              
              <div className="text-center">
                <button className="btn-primary text-lg px-8 py-3">
                  Calculate My Quote
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Relocation Services
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              From residential moves to vehicle transportation, we provide end-to-end 
              solutions for your international relocation needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service 1 */}
            <div className="card-hover p-8 text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <div className="w-8 h-8 bg-primary-600 rounded"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Residential Relocation
              </h3>
              <p className="text-gray-600 mb-6">
                Complete household moving services including packing, transportation, 
                and unpacking at your new destination.
              </p>
              <button className="text-primary-600 font-medium hover:text-primary-700">
                Learn More →
              </button>
            </div>
            
            {/* Service 2 */}
            <div className="card-hover p-8 text-center">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <div className="w-8 h-8 bg-secondary-600 rounded"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Vehicle Transportation
              </h3>
              <p className="text-gray-600 mb-6">
                Safe and secure transportation of your vehicles, from cars to 
                motorcycles, with full insurance coverage.
              </p>
              <button className="text-secondary-600 font-medium hover:text-secondary-700">
                Learn More →
              </button>
            </div>
            
            {/* Service 3 */}
            <div className="card-hover p-8 text-center">
              <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <div className="w-8 h-8 bg-accent-600 rounded"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Documentation Support
              </h3>
              <p className="text-gray-600 mb-6">
                Complete assistance with visas, customs, and all paperwork 
                required for your international move.
              </p>
              <button className="text-accent-600 font-medium hover:text-accent-700">
                Learn More →
              </button>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default HomePage;
