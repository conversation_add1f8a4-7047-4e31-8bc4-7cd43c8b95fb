{"name": "vay<PERSON><PERSON>-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.4", "react-hook-form": "^7.47.0", "react-query": "^3.39.3", "axios": "^1.6.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.2", "swiper": "^10.3.1", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.292.0"}, "devDependencies": {"@types/node": "^20.8.7", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@types/leaflet": "^1.9.8", "typescript": "^5.2.2", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}