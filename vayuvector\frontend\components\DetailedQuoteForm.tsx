import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { 
  ArrowRightIcon, 
  ArrowLeftIcon,
  CheckCircleIcon,
  EnvelopeIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';

interface QuoteFormData {
  // Customer Information
  customer: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  
  // Location Information
  origin: {
    country: string;
    city: string;
    address?: string;
    zipCode?: string;
  };
  
  destination: {
    country: string;
    city: string;
    address?: string;
    zipCode?: string;
  };
  
  // Move Details
  moveDetails: {
    homeSize: string;
    moveDate: string;
    flexibility: string;
    packingService: boolean;
    storageNeeded: boolean;
    storageDuration: number;
    specialItems: string[];
    additionalServices: string[];
  };
  
  // Vehicles (optional)
  vehicles: Array<{
    type: string;
    make: string;
    model: string;
    year: number;
    condition: string;
  }>;
}

const DetailedQuoteForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [quoteResult, setQuoteResult] = useState<any>(null);
  const [savedQuoteId, setSavedQuoteId] = useState<string | null>(null);
  
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<QuoteFormData>();
  
  const totalSteps = 5;
  
  const specialItemsOptions = [
    'Piano',
    'Artwork',
    'Antiques',
    'Wine Collection',
    'Electronics',
    'Plants',
    'Pets',
    'Fragile Items',
  ];
  
  const additionalServicesOptions = [
    'Cleaning Service',
    'Handyman Service',
    'Utility Setup',
    'School Enrollment',
    'Banking Setup',
    'Pet Relocation',
    'Temporary Accommodation',
    'Cultural Orientation',
  ];

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data: QuoteFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create quote');
      }

      const result = await response.json();
      setQuoteResult(result.data);
      setSavedQuoteId(result.data._id);
      setCurrentStep(totalSteps + 1); // Results step
      toast.success('Quote created successfully!');
    } catch (error) {
      toast.error('Failed to create quote. Please try again.');
      console.error('Quote creation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveQuoteForLater = async () => {
    const formData = watch();
    try {
      const response = await fetch('/api/quotes/save-draft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...formData, status: 'draft' }),
      });

      if (!response.ok) {
        throw new Error('Failed to save quote');
      }

      const result = await response.json();
      setSavedQuoteId(result.data._id);
      toast.success('Quote saved for later!');
    } catch (error) {
      toast.error('Failed to save quote.');
      console.error('Save quote error:', error);
    }
  };

  const emailQuote = async () => {
    if (!quoteResult) return;
    
    try {
      const response = await fetch(`/api/quotes/${quoteResult._id}/email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to email quote');
      }

      toast.success('Quote emailed successfully!');
    } catch (error) {
      toast.error('Failed to email quote.');
      console.error('Email quote error:', error);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Contact Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name *
                </label>
                <input
                  {...register('customer.firstName', { required: 'First name is required' })}
                  className="input"
                  placeholder="Enter your first name"
                />
                {errors.customer?.firstName && (
                  <p className="text-red-500 text-sm mt-1">{errors.customer.firstName.message}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name *
                </label>
                <input
                  {...register('customer.lastName', { required: 'Last name is required' })}
                  className="input"
                  placeholder="Enter your last name"
                />
                {errors.customer?.lastName && (
                  <p className="text-red-500 text-sm mt-1">{errors.customer.lastName.message}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  {...register('customer.email', { 
                    required: 'Email is required',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  className="input"
                  placeholder="Enter your email"
                />
                {errors.customer?.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.customer.email.message}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  {...register('customer.phone', { required: 'Phone number is required' })}
                  className="input"
                  placeholder="Enter your phone number"
                />
                {errors.customer?.phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.customer.phone.message}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Moving Locations
            </h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Origin */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Moving From</h4>
                <div className="space-y-4">
                  <input
                    {...register('origin.country', { required: 'Origin country is required' })}
                    className="input"
                    placeholder="Country"
                  />
                  <input
                    {...register('origin.city', { required: 'Origin city is required' })}
                    className="input"
                    placeholder="City"
                  />
                  <input
                    {...register('origin.address')}
                    className="input"
                    placeholder="Address (optional)"
                  />
                  <input
                    {...register('origin.zipCode')}
                    className="input"
                    placeholder="Zip Code (optional)"
                  />
                </div>
              </div>
              
              {/* Destination */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Moving To</h4>
                <div className="space-y-4">
                  <input
                    {...register('destination.country', { required: 'Destination country is required' })}
                    className="input"
                    placeholder="Country"
                  />
                  <input
                    {...register('destination.city', { required: 'Destination city is required' })}
                    className="input"
                    placeholder="City"
                  />
                  <input
                    {...register('destination.address')}
                    className="input"
                    placeholder="Address (optional)"
                  />
                  <input
                    {...register('destination.zipCode')}
                    className="input"
                    placeholder="Zip Code (optional)"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Move Details
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Home Size *
                </label>
                <select
                  {...register('moveDetails.homeSize', { required: 'Home size is required' })}
                  className="input"
                >
                  <option value="">Select home size</option>
                  <option value="studio">Studio</option>
                  <option value="1-bedroom">1 Bedroom</option>
                  <option value="2-bedroom">2 Bedroom</option>
                  <option value="3-bedroom">3 Bedroom</option>
                  <option value="4-bedroom">4 Bedroom</option>
                  <option value="5-bedroom">5+ Bedroom</option>
                  <option value="office">Office</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Move Date *
                </label>
                <input
                  type="date"
                  {...register('moveDetails.moveDate', { required: 'Move date is required' })}
                  className="input"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date Flexibility
                </label>
                <select
                  {...register('moveDetails.flexibility')}
                  className="input"
                >
                  <option value="exact">Exact date</option>
                  <option value="within-week">Within a week</option>
                  <option value="within-month">Within a month</option>
                </select>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register('moveDetails.packingService')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <label className="ml-2 text-gray-700">
                  Professional packing service
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register('moveDetails.storageNeeded')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <label className="ml-2 text-gray-700">
                  Storage service needed
                </label>
              </div>
              
              {watch('moveDetails.storageNeeded') && (
                <div className="ml-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Storage Duration (months)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="24"
                    {...register('moveDetails.storageDuration')}
                    className="input w-32"
                  />
                </div>
              )}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Special Items & Additional Services
            </h3>
            
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Special Items</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {specialItemsOptions.map((item) => (
                    <label key={item} className="flex items-center">
                      <input
                        type="checkbox"
                        value={item}
                        {...register('moveDetails.specialItems')}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{item}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Additional Services</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {additionalServicesOptions.map((service) => (
                    <label key={service} className="flex items-center">
                      <input
                        type="checkbox"
                        value={service}
                        {...register('moveDetails.additionalServices')}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{service}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Review & Submit
            </h3>
            
            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <div>
                <h4 className="font-medium text-gray-900">Contact</h4>
                <p className="text-gray-600">
                  {watch('customer.firstName')} {watch('customer.lastName')} - {watch('customer.email')}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">Move Route</h4>
                <p className="text-gray-600">
                  {watch('origin.city')}, {watch('origin.country')} → {watch('destination.city')}, {watch('destination.country')}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">Move Details</h4>
                <p className="text-gray-600">
                  {watch('moveDetails.homeSize')} on {watch('moveDetails.moveDate')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center justify-center space-x-4">
              <button
                type="button"
                onClick={saveQuoteForLater}
                className="btn-outline flex items-center space-x-2"
              >
                <BookmarkIcon className="h-4 w-4" />
                <span>Save for Later</span>
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="btn-primary flex items-center space-x-2"
              >
                {loading ? (
                  <div className="spinner"></div>
                ) : (
                  <>
                    <CheckCircleIcon className="h-4 w-4" />
                    <span>Get My Quote</span>
                  </>
                )}
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (quoteResult) {
    return (
      <div className="card p-8 max-w-2xl mx-auto text-center">
        <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-6" />
        <h3 className="text-2xl font-bold text-gray-900 mb-4">
          Quote Created Successfully!
        </h3>
        <p className="text-gray-600 mb-6">
          Quote #{quoteResult.quoteNumber} has been created and saved.
        </p>
        
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <div className="text-3xl font-bold text-primary-600 mb-2">
            ${quoteResult.pricing.totalPrice.toLocaleString()}
          </div>
          <div className="text-gray-600">
            Valid until {new Date(quoteResult.pricing.validUntil).toLocaleDateString()}
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={emailQuote}
            className="btn-primary flex items-center justify-center space-x-2"
          >
            <EnvelopeIcon className="h-4 w-4" />
            <span>Email Quote</span>
          </button>
          <button
            onClick={() => window.location.reload()}
            className="btn-outline"
          >
            Create New Quote
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="card p-8 max-w-4xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Step {currentStep} of {totalSteps}
          </span>
          <span className="text-sm text-gray-500">
            {savedQuoteId && 'Saved'}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStep()}
        </motion.div>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <button
            type="button"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="btn-ghost disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            <span>Previous</span>
          </button>
          
          {currentStep < totalSteps ? (
            <button
              type="button"
              onClick={nextStep}
              className="btn-primary flex items-center space-x-2"
            >
              <span>Next</span>
              <ArrowRightIcon className="h-4 w-4" />
            </button>
          ) : null}
        </div>
      </form>
    </div>
  );
};

export default DetailedQuoteForm;
